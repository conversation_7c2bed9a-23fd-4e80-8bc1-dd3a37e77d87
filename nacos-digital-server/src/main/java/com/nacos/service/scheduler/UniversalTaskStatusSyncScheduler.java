package com.nacos.service.scheduler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nacos.config.UniversalTaskConfig;
import com.nacos.entity.enums.TaskType;
import com.nacos.entity.enums.UnifiedTaskStatusEnum;
import com.nacos.entity.po.VideoTranslateTaskPO;
import com.nacos.entity.po.DigitalAudioTaskPO;
import com.nacos.mapper.VideoTranslateTaskMapper;
import com.nacos.mapper.DigitalAudioTaskMapper;
import com.nacos.result.Result;
import com.nacos.service.VideoTranslateAsyncService;
import com.nacos.service.factory.TaskProcessorFactory;
import com.nacos.service.IDigitalAudioTaskService;
import com.google.common.util.concurrent.RateLimiter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 统一任务状态同步调度器
 * 
 * 负责定时查询各种任务类型的处理状态并更新到数据库。
 * 采用智能分阶段查询策略，大幅减少API调用频率。
 * 
 * 主要功能：
 * 1. 分阶段查询策略：新任务30秒查询，老任务1分钟查询
 * 2. API限流保护：全局限流 + 服务商限流 + 查询缓存
 * 3. 优先级队列：优先处理接近完成时间的任务
 * 4. 批量处理：按服务商分组，减少网络开销
 * 5. 统计监控：详细的同步统计和性能监控
 * 
 * 设计原则：
 * 1. 智能调度：基于任务年龄的分阶段查询策略
 * 2. 限流保护：多层次的API调用频率控制
 * 3. 缓存机制：避免短时间内重复查询
 * 4. 错误隔离：单个任务失败不影响其他任务
 * 5. 可观测性：完整的监控、统计和告警机制
 * 
 * 查询策略：
 * - 阶段1 (0-5分钟)：每30秒查询一次 - 快速检测短视频完成
 * - 阶段2 (5分钟以上)：每1分钟查询一次 - 正常检测中长视频完成
 * 
 * <AUTHOR>
 * @since 2025-08-05
 * @version 1.0 - 智能状态同步调度架构
 */
@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "universal-task.status.sync", name = "enabled", havingValue = "true", matchIfMissing = true)
public class UniversalTaskStatusSyncScheduler {

    private final TaskProcessorFactory processorFactory;
    private final VideoTranslateAsyncService videoTranslateAsyncService;
    private final IDigitalAudioTaskService digitalAudioTaskService;
    private final UniversalTaskConfig config;
    private final VideoTranslateTaskMapper videoTranslateTaskMapper;
    private final DigitalAudioTaskMapper digitalAudioTaskMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * API调用限流器
     */
    private volatile RateLimiter globalRateLimiter;
    private final Map<String, RateLimiter> providerRateLimiters = new ConcurrentHashMap<>();
    
    /**
     * 任务查询缓存（避免重复查询）
     */
    private final Map<String, Long> lastQueryTime = new ConcurrentHashMap<>();
    
    /**
     * 同步统计信息
     */
    private final Map<String, Object> syncStats = new ConcurrentHashMap<>();
    
    /**
     * 服务启动时间
     */
    private final long startupTime = System.currentTimeMillis();
    
    // ==================== 主要定时任务 ====================
    
    /**
     * 智能任务状态同步调度器
     * 执行频率：每30秒扫描一次，但使用智能策略决定实际查询
     */
    @Scheduled(fixedRateString = "${universal-task.status.sync.base-scan-rate:30000}")
    public void intelligentTaskStatusSync() {
        String methodName = "intelligentTaskStatusSync";
        long startTime = System.currentTimeMillis();
        
        try {
            log.debug("[{}] 开始智能任务状态同步扫描", methodName);
            
            // 初始化限流器（如果需要）
            initializeRateLimitersIfNeeded();
            
            // 1. 获取需要同步的任务（按优先级排序）
            List<TaskSyncCandidate> candidates = getPrioritizedSyncCandidates();
            
            if (candidates.isEmpty()) {
                log.debug("[{}] 没有需要同步的任务", methodName);
                return;
            }
            
            // 2. 应用智能过滤策略
            List<TaskSyncCandidate> filteredTasks = applyIntelligentFiltering(candidates);
            
            // 3. 按服务商分组，避免对同一服务商的频繁调用
            Map<String, List<TaskSyncCandidate>> groupedByProvider = 
                filteredTasks.stream().collect(Collectors.groupingBy(TaskSyncCandidate::getProvider));
            
            // 4. 限流执行同步
            int totalSynced = 0;
            for (Map.Entry<String, List<TaskSyncCandidate>> entry : groupedByProvider.entrySet()) {
                String provider = entry.getKey();
                List<TaskSyncCandidate> tasks = entry.getValue();
                
                int synced = syncTasksWithRateLimit(provider, tasks);
                totalSynced += synced;
            }
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("[{}] 智能同步完成: 候选任务={}, 过滤后={}, 实际同步={}, 耗时={}ms", 
                    methodName, candidates.size(), filteredTasks.size(), totalSynced, duration);
            
            // 更新统计信息
            incrementStat("totalScans");
            addToStat("totalCandidates", candidates.size());
            addToStat("totalFiltered", filteredTasks.size());
            addToStat("totalSynced", totalSynced);
            setStat("lastSyncTime", System.currentTimeMillis());

            // 记录执行时间统计
            updateExecutionTimeStats(duration);
                    
        } catch (Exception e) {
            log.error("[{}] 智能任务状态同步异常", methodName, e);
            incrementStat("errorCount");
        }
    }
    
    // ==================== 内部类定义 ====================
    
    /**
     * 任务同步候选对象
     */
    public static class TaskSyncCandidate {
        private String taskId;
        private String provider;
        private long submitTime;
        private TaskType taskType;
        
        public TaskSyncCandidate(String taskId, String provider, long submitTime, TaskType taskType) {
            this.taskId = taskId;
            this.provider = provider;
            this.submitTime = submitTime;
            this.taskType = taskType;
        }
        
        // Getters
        public String getTaskId() { return taskId; }
        public String getProvider() { return provider; }
        public long getSubmitTime() { return submitTime; }
        public TaskType getTaskType() { return taskType; }
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 初始化限流器
     */
    private void initializeRateLimitersIfNeeded() {
        if (globalRateLimiter == null) {
            synchronized (this) {
                if (globalRateLimiter == null) {
                    int maxCallsPerMinute = config.getStatus().getSync().getRateLimit().getMaxCallsPerMinute();
                    globalRateLimiter = RateLimiter.create(maxCallsPerMinute / 60.0); // 转换为每秒速率
                    log.info("初始化全局限流器: {}次/分钟", maxCallsPerMinute);
                }
            }
        }
    }
    
    /**
     * 增加统计计数
     */
    private void incrementStat(String key) {
        syncStats.compute(key, (k, v) -> {
            if (v instanceof Long) {
                return ((Long) v) + 1;
            }
            return 1L;
        });
    }

    /**
     * 增加统计数值
     */
    private void addToStat(String key, long value) {
        syncStats.compute(key, (k, v) -> {
            if (v instanceof Long) {
                return ((Long) v) + value;
            }
            return value;
        });
    }

    /**
     * 设置统计值
     */
    private void setStat(String key, Object value) {
        syncStats.put(key, value);
    }

    /**
     * 更新执行时间统计
     */
    private void updateExecutionTimeStats(long duration) {
        // 更新平均执行时间
        Long totalScans = (Long) syncStats.getOrDefault("totalScans", 0L);
        Long totalExecutionTime = (Long) syncStats.getOrDefault("totalExecutionTime", 0L);
        totalExecutionTime += duration;
        setStat("totalExecutionTime", totalExecutionTime);

        if (totalScans > 0) {
            setStat("averageSyncTime", totalExecutionTime / totalScans);
        }

        // 更新最大和最小执行时间
        Long maxSyncTime = (Long) syncStats.getOrDefault("maxSyncTime", 0L);
        Long minSyncTime = (Long) syncStats.getOrDefault("minSyncTime", Long.MAX_VALUE);

        if (duration > maxSyncTime) {
            setStat("maxSyncTime", duration);
        }
        if (duration < minSyncTime) {
            setStat("minSyncTime", duration);
        }
    }

    /**
     * 更新同步统计信息（兼容旧方法）
     */
    private void updateSyncStats(String key, Object value) {
        if (value instanceof Number) {
            addToStat(key, ((Number) value).longValue());
        } else {
            setStat(key, value);
        }
    }
    
    /**
     * 获取优先级排序的同步候选任务
     */
    private List<TaskSyncCandidate> getPrioritizedSyncCandidates() {
        List<TaskSyncCandidate> candidates = new ArrayList<>();

        try {
            // 查询视频翻译任务（进行中状态）
            List<VideoTranslateTaskPO> videoTasks = videoTranslateTaskMapper.selectList(
                new LambdaQueryWrapper<VideoTranslateTaskPO>()
                    .eq(VideoTranslateTaskPO::getStatus, UnifiedTaskStatusEnum.PROGRESS.getCode())
                    .orderByAsc(VideoTranslateTaskPO::getCreatedTime)
            );

            for (VideoTranslateTaskPO task : videoTasks) {
                candidates.add(new TaskSyncCandidate(
                    task.getTaskId(),
                    task.getProvider() != null ? task.getProvider() : "default",
                    task.getCreatedTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(),
                    TaskType.VIDEO_TRANSLATE
                ));
            }

            // // 查询音频生成任务（进行中状态）
            // List<DigitalAudioTaskPO> audioTasks = digitalAudioTaskMapper.selectList(
            //     new LambdaQueryWrapper<DigitalAudioTaskPO>()
            //         .eq(DigitalAudioTaskPO::getStatus, UnifiedTaskStatusEnum.PROGRESS.getCode())
            //         .orderByAsc(DigitalAudioTaskPO::getCreatedTime)
            // );

            // for (DigitalAudioTaskPO task : audioTasks) {
            //     candidates.add(new TaskSyncCandidate(
            //         task.getTaskId(),
            //         task.getProvider() != null ? task.getProvider() : "default",
            //         task.getCreatedTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(),
            //         TaskType.AUDIO_GENERATE
            //     ));
            // }

            // 按提交时间排序，优先处理较早的任务
            candidates.sort(Comparator.comparingLong(TaskSyncCandidate::getSubmitTime));

        } catch (Exception e) {
            log.error("获取同步候选任务异常", e);
        }

        return candidates;
    }

    /**
     * 应用智能过滤策略
     */
    private List<TaskSyncCandidate> applyIntelligentFiltering(List<TaskSyncCandidate> candidates) {
        return candidates.stream()
            .filter(this::shouldSyncNow)  // 基于时间阶段判断是否应该查询
            .filter(this::notRecentlyQueried)  // 避免重复查询
            .collect(Collectors.toList());
    }

    /**
     * 判断任务是否应该在当前时间同步（两阶段策略）
     */
    private boolean shouldSyncNow(TaskSyncCandidate task) {
        long taskAge = System.currentTimeMillis() - task.getSubmitTime();
        UniversalTaskConfig.StageConfig stages = config.getStatus().getSync().getStages();

        // 阶段1：0-5分钟，每30秒查询一次
        if (taskAge < stages.getStage1EndMs()) {
            return taskAge % stages.getStage1IntervalMs() < 30000; // 30秒容差
        }

        // 阶段2：5分钟以上，每1分钟查询一次
        return taskAge % stages.getStage2IntervalMs() < 60000; // 1分钟容差
    }

    /**
     * 检查是否最近已查询过
     */
    private boolean notRecentlyQueried(TaskSyncCandidate task) {
        Long lastQuery = lastQueryTime.get(task.getTaskId());
        if (lastQuery == null) {
            return true;
        }

        long cacheTime = config.getStatus().getSync().getRateLimit().getCacheTimeMs();
        return System.currentTimeMillis() - lastQuery > cacheTime;
    }

    /**
     * 限流执行任务同步
     */
    private int syncTasksWithRateLimit(String provider, List<TaskSyncCandidate> tasks) {
        if (tasks.isEmpty()) {
            return 0;
        }

        // 获取或创建服务商限流器
        RateLimiter providerLimiter = providerRateLimiters.computeIfAbsent(provider, p -> {
            int maxConcurrent = config.getStatus().getSync().getRateLimit().getMaxConcurrentPerProvider();
            return RateLimiter.create(maxConcurrent);
        });

        int syncedCount = 0;
        for (TaskSyncCandidate task : tasks) {
            // 全局限流检查
            if (!globalRateLimiter.tryAcquire()) {
                log.debug("全局限流触发，跳过任务: {}", task.getTaskId());
                incrementStat("globalRateLimitHits");
                break;
            }

            // 服务商限流检查
            if (!providerLimiter.tryAcquire()) {
                log.debug("服务商{}限流触发，跳过任务: {}", provider, task.getTaskId());
                incrementStat("providerRateLimitHits");
                break;
            }

            try {
                // 执行实际的状态同步
                boolean synced = performTaskSync(task);
                if (synced) {
                    syncedCount++;
                    // 记录查询时间
                    lastQueryTime.put(task.getTaskId(), System.currentTimeMillis());
                }
            } catch (Exception e) {
                log.error("同步任务{}异常: {}", task.getTaskId(), e.getMessage(), e);
            }
        }

        return syncedCount;
    }

    /**
     * 执行实际的任务状态同步
     */
    private boolean performTaskSync(TaskSyncCandidate task) {
        String methodName = "performTaskSync";
        long startTime = System.currentTimeMillis();

        try {
            log.debug("[{}] 开始同步任务状态: taskId={}, taskType={}, provider={}",
                     methodName, task.getTaskId(), task.getTaskType(), task.getProvider());

            if (task.getTaskType() == TaskType.VIDEO_TRANSLATE) {
                return syncVideoTranslateTask(task);
            } 
            // else if (task.getTaskType() == TaskType.AUDIO_GENERATE) {
            //     return syncAudioGenerateTask(task);
            // }

            log.warn("[{}] 不支持的任务类型: taskId={}, taskType={}",
                    methodName, task.getTaskId(), task.getTaskType());
            return false;

        } catch (Exception e) {
            log.error("[{}] 执行任务{}状态同步失败", methodName, task.getTaskId(), e);
            return false;
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            log.debug("[{}] 任务状态同步完成: taskId={}, 耗时={}ms",
                     methodName, task.getTaskId(), duration);
        }
    }

    /**
     * 同步视频翻译任务状态
     */
    private boolean syncVideoTranslateTask(TaskSyncCandidate task) {
        String methodName = "syncVideoTranslateTask";

        try {
            // 查询任务详细信息
            VideoTranslateTaskPO taskPO = videoTranslateTaskMapper.selectOne(
                new LambdaQueryWrapper<VideoTranslateTaskPO>()
                    .eq(VideoTranslateTaskPO::getTaskId, task.getTaskId())
            );

            if (taskPO == null) {
                log.warn("[{}] 任务不存在: taskId={}", methodName, task.getTaskId());
                return false;
            }

            // 检查任务是否仍在处理中
            if (!UnifiedTaskStatusEnum.PROGRESS.getCode().equals(taskPO.getStatus())) {
                log.debug("[{}] 任务状态已变更，跳过同步: taskId={}, status={}",
                         methodName, task.getTaskId(), taskPO.getStatus());
                return true; // 状态已变更，认为同步成功
            }

            // 调用现有的单任务状态同步逻辑
            Result<Map<String, Object>> result = videoTranslateAsyncService.syncTaskStatus(taskPO);

            if (result.isSuccess()) {
                log.debug("[{}] 视频翻译任务状态同步成功: taskId={}", methodName, task.getTaskId());
                incrementStat("videoTranslateTasksSynced");
                return true;
            } else {
                log.warn("[{}] 视频翻译任务状态同步失败: taskId={}, message={}",
                        methodName, task.getTaskId(), result.getMessage());
                incrementStat("videoTranslateSyncFailures");
                return false;
            }

        } catch (Exception e) {
            log.error("[{}] 同步视频翻译任务异常: taskId={}", methodName, task.getTaskId(), e);
            return false;
        }
    }

    /**
     * 同步音频生成任务状态
     */
    private boolean syncAudioGenerateTask(TaskSyncCandidate task) {
        String methodName = "syncAudioGenerateTask";

        try {
            // 查询任务详细信息
            DigitalAudioTaskPO taskPO = digitalAudioTaskMapper.selectOne(
                new LambdaQueryWrapper<DigitalAudioTaskPO>()
                    .eq(DigitalAudioTaskPO::getTaskId, task.getTaskId())
            );

            if (taskPO == null) {
                log.warn("[{}] 音频任务不存在: taskId={}", methodName, task.getTaskId());
                return false;
            }

            // 检查任务是否仍在处理中
            if (!UnifiedTaskStatusEnum.PROGRESS.getCode().equals(taskPO.getStatus())) {
                log.debug("[{}] 音频任务状态已变更，跳过同步: taskId={}, status={}",
                         methodName, task.getTaskId(), taskPO.getStatus());
                return true; // 状态已变更，认为同步成功
            }

            // 音频生成是同步处理，主要检查超时任务
            long taskAge = System.currentTimeMillis() - task.getSubmitTime();
            long timeoutThreshold = 10 * 60 * 1000; // 10分钟超时阈值

            if (taskAge > timeoutThreshold) {
                log.warn("[{}] 音频任务处理超时，标记为失败: taskId={}, taskAge={}ms",
                        methodName, task.getTaskId(), taskAge);

                // 标记超时任务为失败
                boolean updated = digitalAudioTaskService.updateTaskStatus(
                    task.getTaskId(),
                    UnifiedTaskStatusEnum.FAILED.getCode(),
                    "任务处理超时，超过10分钟未完成"
                );

                if (updated) {
                    log.info("[{}] 超时音频任务已标记为失败: taskId={}", methodName, task.getTaskId());
                    incrementStat("timeoutTasksMarked");
                    incrementStat("audioGenerateTasksSynced");
                    return true;
                } else {
                    log.error("[{}] 更新超时音频任务状态失败: taskId={}", methodName, task.getTaskId());
                    incrementStat("audioTimeoutUpdateFailures");
                    return false;
                }
            } else {
                // 任务仍在合理时间范围内，继续等待
                log.debug("[{}] 音频任务仍在处理中，等待完成: taskId={}, taskAge={}ms",
                         methodName, task.getTaskId(), taskAge);
                incrementStat("audioTasksWaiting");
                return true;
            }

        } catch (Exception e) {
            log.error("[{}] 同步音频生成任务异常: taskId={}", methodName, task.getTaskId(), e);
            return false;
        }
    }

    /**
     * 获取同步统计信息
     */
    public Map<String, Object> getSyncStatistics() {
        Map<String, Object> stats = new HashMap<>(syncStats);
        stats.put("startupTime", startupTime);
        stats.put("currentTime", System.currentTimeMillis());
        stats.put("uptime", System.currentTimeMillis() - startupTime);
        stats.put("cacheSize", lastQueryTime.size());
        stats.put("providerRateLimiters", providerRateLimiters.size());

        // 计算成功率
        Long totalSynced = (Long) stats.getOrDefault("totalSynced", 0L);
        Long errorCount = (Long) stats.getOrDefault("errorCount", 0L);
        if (totalSynced + errorCount > 0) {
            double successRate = (double) totalSynced / (totalSynced + errorCount) * 100;
            stats.put("successRate", String.format("%.2f%%", successRate));
        } else {
            stats.put("successRate", "N/A");
        }

        return stats;
    }

    /**
     * 获取调度器健康状态
     */
    public Map<String, Object> getHealthStatus() {
        Map<String, Object> health = new HashMap<>();

        // 基本状态
        health.put("enabled", true);
        health.put("schedulerRunning", true);
        health.put("startupTime", startupTime);
        health.put("uptime", System.currentTimeMillis() - startupTime);

        // 组件健康状态
        health.put("globalRateLimiterInitialized", globalRateLimiter != null);
        health.put("providerRateLimitersCount", providerRateLimiters.size());
        health.put("cacheSize", lastQueryTime.size());

        // 最近执行状态
        Long lastSyncTime = (Long) syncStats.get("lastSyncTime");
        health.put("lastSyncTime", lastSyncTime);
        if (lastSyncTime != null) {
            long timeSinceLastSync = System.currentTimeMillis() - lastSyncTime;
            health.put("timeSinceLastSync", timeSinceLastSync);
            health.put("recentlyActive", timeSinceLastSync < 300000); // 5分钟内活跃
        } else {
            health.put("timeSinceLastSync", null);
            health.put("recentlyActive", false);
        }

        // 错误率检查
        Long totalScans = (Long) syncStats.getOrDefault("totalScans", 0L);
        Long errorCount = (Long) syncStats.getOrDefault("errorCount", 0L);
        if (totalScans > 0) {
            double errorRate = (double) errorCount / totalScans * 100;
            health.put("errorRate", String.format("%.2f%%", errorRate));
            health.put("healthy", errorRate < 10.0); // 错误率低于10%认为健康
        } else {
            health.put("errorRate", "0.00%");
            health.put("healthy", true);
        }

        return health;
    }

    /**
     * 获取详细的执行统计信息
     */
    public Map<String, Object> getExecutionStatistics() {
        Map<String, Object> execStats = new HashMap<>();

        // 基本执行统计
        execStats.put("totalScans", syncStats.getOrDefault("totalScans", 0L));
        execStats.put("totalCandidates", syncStats.getOrDefault("totalCandidates", 0L));
        execStats.put("totalFiltered", syncStats.getOrDefault("totalFiltered", 0L));
        execStats.put("totalSynced", syncStats.getOrDefault("totalSynced", 0L));
        execStats.put("errorCount", syncStats.getOrDefault("errorCount", 0L));

        // 任务类型统计
        execStats.put("videoTranslateTasksSynced", syncStats.getOrDefault("videoTranslateTasksSynced", 0L));
        execStats.put("audioGenerateTasksSynced", syncStats.getOrDefault("audioGenerateTasksSynced", 0L));
        execStats.put("timeoutTasksMarked", syncStats.getOrDefault("timeoutTasksMarked", 0L));

        // 性能统计
        execStats.put("averageSyncTime", syncStats.getOrDefault("averageSyncTime", 0L));
        execStats.put("maxSyncTime", syncStats.getOrDefault("maxSyncTime", 0L));
        execStats.put("minSyncTime", syncStats.getOrDefault("minSyncTime", 0L));

        // 限流统计
        execStats.put("globalRateLimitHits", syncStats.getOrDefault("globalRateLimitHits", 0L));
        execStats.put("providerRateLimitHits", syncStats.getOrDefault("providerRateLimitHits", 0L));

        return execStats;
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        syncStats.clear();
        lastQueryTime.clear();
        log.info("Status sync scheduler statistics reset");
    }

    /**
     * 检查调度器是否健康运行
     */
    public boolean isHealthy() {
        Map<String, Object> health = getHealthStatus();
        return (Boolean) health.getOrDefault("healthy", false);
    }
}
